//  (C) Copyright <PERSON> 2006-8.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_BIG_LANCZOS_HPP
#define BOOST_BIG_LANCZOS_HPP

#include <boost/math/special_functions/lanczos.hpp>

namespace boost{ namespace math{ namespace lanczos{

//
// Lanczos Coefficients for N=13 G=13.144565
// Max experimental error (with arbitrary precision arithmetic) 9.2213e-23
// Generated with compiler: Microsoft Visual C++ version 8.0 on Win32 at Mar 23 2006
//
typedef lanczos13 lanczos13UDT;

//
// Lanczos Coefficients for N=22 G=22.61891
// Max experimental error (with arbitrary precision arithmetic) 2.9524e-38
// Generated with compiler: Microsoft Visual C++ version 8.0 on Win32 at Mar 23 2006
//
struct lanczos22UDT : public std::integral_constant<int, 120>
{
   //
   // Produces slightly better than 128-bit long-double precision when 
   // evaluated at higher precision:
   //
   template <class T>
   static T lanczos_sum(const T& z)
   {
      lanczos_initializer<lanczos22UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      static const T num[22] = {
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 46198410803245094237463011094.12173081986)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 43735859291852324413622037436.321513777)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 19716607234435171720534556386.97481377748)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 5629401471315018442177955161.245623932129)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1142024910634417138386281569.245580222392)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 175048529315951173131586747.695329230778)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 21044290245653709191654675.41581372963167)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 2033001410561031998451380.335553678782601)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 160394318862140953773928.8736211601848891)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 10444944438396359705707.48957290388740896)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 565075825801617290121.1466393747967538948)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 25475874292116227538.99448534450411942597)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 957135055846602154.6720835535232270205725)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 29874506304047462.23662392445173880821515)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 769651310384737.2749087590725764959689181)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 16193289100889.15989633624378404096011797)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 273781151680.6807433264462376754578933261)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 3630485900.32917021712188739762161583295)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 36374352.05577334277856865691538582936484)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 258945.7742115532455441786924971194951043)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1167.501919472435718934219997431551246996)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 2.50662827463100050241576528481104525333))
      };
      static const T denom[22] = {
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 2432902008176640000.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 8752948036761600000.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 13803759753640704000.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 12870931245150988800.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 8037811822645051776.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 3599979517947607200.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1206647803780373360.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 311333643161390640.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 63030812099294896.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 10142299865511450.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1307535010540395.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 135585182899530.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 11310276995381.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 756111184500.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 40171771630.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1672280820.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 53327946.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1256850.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 20615.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 210.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1.0))
      };
      return boost::math::tools::evaluate_rational(num, denom, z);
   }

   template <class T>
   static T lanczos_sum_expG_scaled(const T& z)
   {
      lanczos_initializer<lanczos22UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      static const T num[22] = {
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 6939996264376682180.277485395074954356211)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 6570067992110214451.87201438870245659384)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 2961859037444440551.986724631496417064121)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 845657339772791245.3541226499766163431651)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 171556737035449095.2475716923888737881837)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 26296059072490867.7822441885603400926007)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 3161305619652108.433798300149816829198706)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 305400596026022.4774396904484542582526472)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 24094681058862.55120507202622377623528108)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1569055604375.919477574824168939428328839)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 84886558909.02047889339710230696942513159)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 3827024985.166751989686050643579753162298)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 143782298.9273215199098728674282885500522)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 4487794.24541641841336786238909171265944)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 115618.2025760830513505888216285273541959)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 2432.580773108508276957461757328744780439)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 41.12782532742893597168530008461874360191)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.5453771709477689805460179187388702295792)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.005464211062612080347167337964166505282809)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.388992321263586767037090706042788910953e-4)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.1753839324538447655939518484052327068859e-6)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.3765495513732730583386223384116545391759e-9))
      };
      static const T denom[22] = {
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 2432902008176640000.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 8752948036761600000.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 13803759753640704000.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 12870931245150988800.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 8037811822645051776.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 3599979517947607200.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1206647803780373360.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 311333643161390640.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 63030812099294896.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 10142299865511450.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1307535010540395.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 135585182899530.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 11310276995381.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 756111184500.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 40171771630.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1672280820.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 53327946.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1256850.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 20615.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 210.0)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1.0))
      };
      return boost::math::tools::evaluate_rational(num, denom, z);
   }


   template<class T>
   static T lanczos_sum_near_1(const T& dz)
   {
      lanczos_initializer<lanczos22UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      static const T d[21] = {
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 8.318998691953337183034781139546384476554)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -63.15415991415959158214140353299240638675)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 217.3108224383632868591462242669081540163)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -448.5134281386108366899784093610397354889)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 619.2903759363285456927248474593012711346)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -604.1630177420625418522025080080444177046)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 428.8166750424646119935047118287362193314)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -224.6988753721310913866347429589434550302)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 87.32181627555510833499451817622786940961)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -25.07866854821128965662498003029199058098)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 5.264398125689025351448861011657789005392)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.792518936256495243383586076579921559914)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.08317448364744713773350272460937904691566)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.005845345166274053157781068150827567998882)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.0002599412126352082483326238522490030412391)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.6748102079670763884917431338234783496303e-5)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.908824383434109002762325095643458603605e-7)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.5299325929309389890892469299969669579725e-9)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.994306085859549890267983602248532869362e-12)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.3499893692975262747371544905820891835298e-15)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.7260746353663365145454867069182884694961e-20)),
      };
      T result = 0;
      for(unsigned k = 1; k <= sizeof(d)/sizeof(d[0]); ++k)
      {
         result += (-d[k-1]*dz)/(k*dz + k*k);
      }
      return result;
   }

   template<class T>
   static T lanczos_sum_near_2(const T& dz)
   {
      static const T d[21] = {
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 75.39272007105208086018421070699575462226)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -572.3481967049935412452681346759966390319)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 1969.426202741555335078065370698955484358)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -4064.74968778032030891520063865996757519)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 5612.452614138013929794736248384309574814)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -5475.357667500026172903620177988213902339)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 3886.243614216111328329547926490398103492)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -2036.382026072125407192448069428134470564)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 791.3727954936062108045551843636692287652)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -227.2808432388436552794021219198885223122)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 47.70974355562144229897637024320739257284)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -7.182373807798293545187073539819697141572)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.7537866989631514559601547530490976100468)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.05297470142240154822658739758236594717787)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.00235577330936380542539812701472320434133)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.6115613067659273118098229498679502138802e-4)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.8236417010170941915758315020695551724181e-6)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.4802628430993048190311242611330072198089e-8)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.9011113376981524418952720279739624707342e-11)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, -0.3171854152689711198382455703658589996796e-14)),
         static_cast<T>(BOOST_MATH_BIG_CONSTANT(T, 120, 0.6580207998808093935798753964580596673177e-19)),
      };
      T result = 0;
      T z = dz + 2;
      for(unsigned k = 1; k <= sizeof(d)/sizeof(d[0]); ++k)
      {
         result += (-d[k-1]*dz)/(z + k*z + k*k - 1);
      }
      return result;
   }

   static double g(){ return 22.61890999999999962710717227309942245483; }
};
//
// Lanczos Coefficients for N=31 G=32.08067
// Max experimental error (with arbitrary precision arithmetic) 0.162e-52
// Generated with compiler: Microsoft Visual C++ version 8.0 on Win32 at May 9 2006
//
struct lanczos31UDT
{
   template <class T>
   static T lanczos_sum(const T& z)
   {
      lanczos_initializer<lanczos31UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      static const T num[31] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.2579646553333513328235723061836959833277e46)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.2444796504337453845497419271639377138264e46)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.1119885499016017172212179730662673475329e46)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.3301983829072723658949204487793889113715e45)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.7041171040503851585152895336505379417066e44)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.1156687509001223855125097826246939403504e44)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1522559363393940883866575697565974893306000)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 164914363507650839510801418717701057005700)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 14978522943127593263654178827041568394060)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1156707153701375383907746879648168666774)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 76739431129980851159755403434593664173.2)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4407916278928188620282281495575981079.306)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 220487883931812802092792125175269667.3004)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 9644828280794966468052381443992828.433924)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 369996467042247229310044531282837.6549068)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 12468380890717344610932904378961.13494291)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 369289245210898235894444657859.0529720075)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 9607992460262594951559461829.34885209022)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 219225935074853412540086410.981421315799)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4374309943598658046326340.720767382079549)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 76008779092264509404014.10530947173485581)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1143503533822162444712.335663112617754987)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 14779233719977576920.37884890049671578409)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 162409028440678302.9992838032166348069916)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1496561553388385.733407609544964535634135)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 11347624460661.81008311053190661436107043)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 68944915931.32004991941950530448472223832)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 322701221.6391432296123937035480931903651)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1092364.213992634267819050120261755371294)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2380.151399852411512711176940867823024864)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2.506628274631000502415765284811045253007)),
      };
      static const T denom[31] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.8841761993739701954543616e31)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.3502799997985980526649278464e32)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.622621928420356134910574592e32)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 66951000306085302338993639424000)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 49361465831621147825759587123200)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 26751280755793398822580822142976)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 11139316913434780466101123891200)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 3674201658710345201899117607040)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 981347603630155088295475765440)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 215760462268683520394805979744)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 39539238727270799376544542000)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 6097272817323042122728617800)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 796974693974455191377937300)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 88776380550648116217781890)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 8459574446076318147830625)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 691254538651580660999025)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 48487623689430693038025)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2918939500751087661105)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 150566737512021319125)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 6634460278534540725)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 248526574856284725)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 7860403394108265)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 207912996295875)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4539323721075)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 80328850875)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1122686019)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 11921175)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 90335)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 435)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1)),
      };
      return boost::math::tools::evaluate_rational(num, denom, z, 31);
   }

   template <class T>
   static T lanczos_sum_expG_scaled(const T& z)
   {
      lanczos_initializer<lanczos31UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      static const T num[31] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 30137154810677525966583148469478.52374216)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 28561746428637727032849890123131.36314653)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 13083250730789213354063781611435.74046294)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 3857598154697777600846539129354.783647)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 822596651552555685068015316144.0952185852)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 135131964033213842052904200372.039133532)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 17787555889683709693655685146.19771358863)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1926639793777927562221423874.149673297196)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 174989113988888477076973808.6991839697774)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 13513425905835560387095425.01158383184045)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 896521313378762433091075.1446749283094845)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 51496223433749515758124.71524415105430686)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2575886794780078381228.37205955912263407)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 112677328855422964200.4155776009524490958)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4322545967487943330.625233358130724324796)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 145663957202380774.0362027607207590519723)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4314283729473470.686566233465428332496534)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 112246988185485.8877916434026906290603878)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2561143864972.040563435178307062626388193)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 51103611767.9626550674442537989885239605)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 887985348.0369447209508500133077232094491)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 13359172.3954672607019822025834072685839)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 172660.8841147568768783928167105965064459)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1897.370795407433013556725714874693719617)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 17.48383210090980598861217644749573257178)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.1325705316732132940835251054350153028901)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.0008054605783673449641889260501816356090452)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.377001130700104515644336869896819162464e-5)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.1276172868883867038813825443204454996531e-7)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.2780651912081116274907381023821492811093e-10)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.2928410648650955854121639682890739211234e-13)),
      };
      static const T denom[31] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.8841761993739701954543616e31)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.3502799997985980526649278464e32)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.622621928420356134910574592e32)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 66951000306085302338993639424000)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 49361465831621147825759587123200)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 26751280755793398822580822142976)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 11139316913434780466101123891200)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 3674201658710345201899117607040)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 981347603630155088295475765440)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 215760462268683520394805979744)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 39539238727270799376544542000)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 6097272817323042122728617800)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 796974693974455191377937300)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 88776380550648116217781890)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 8459574446076318147830625)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 691254538651580660999025)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 48487623689430693038025)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2918939500751087661105)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 150566737512021319125)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 6634460278534540725)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 248526574856284725)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 7860403394108265)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 207912996295875)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4539323721075)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 80328850875)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1122686019)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 11921175)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 90335)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 435)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1)),
      };
      return boost::math::tools::evaluate_rational(num, denom, z, 31);
   }


   template<class T>
   static T lanczos_sum_near_1(const T& dz)
   {
      lanczos_initializer<lanczos31UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      static const T d[30] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 11.80038544942943603508206880307972596807)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -130.6355975335626214564236363322099481079)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 676.2177719145993049893392276809256538927)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -2174.724497783850503069990936574060452057)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4869.877180638131076410069103742986502022)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -8065.744271864238179992762265472478229172)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 10245.03825618572106228191509520638651539)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -10212.83902362683215459850403668669647192)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 8110.289185383288952562767679576754140336)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -5179.310892558291062401828964000776095156)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2673.987492589052370230989109591011091273)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1118.342574651205183051884250033505609141)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 378.5812742511620662650096436471920295596)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -103.3725999812126067084828735543906768961)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 22.62913974335996321848099677797888917792)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -3.936414819950859548507275533569588041446)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.5376818198843817355682124535902641644854)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.0567827903603478957483409124122554243201)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.004545544993648879420352693271088478106482)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.0002689795568951033950042375135970897959935)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.1139493459006846530734617710847103572122e-4)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.3316581197839213921885210451302820192794e-6)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.6285613334898374028443777562554713906213e-8)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.7222145115734409070310317999856424167091e-10)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.4562976983547274766890241815002584238219e-12)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.1380593023819058919640038942493212141072e-14)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.1629663871586410129307496385264268190679e-17)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.5429994291916548849493889660077076739993e-21)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.2922682842441892106795386303084661338957e-25)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.8456967065309046044689041041336866118459e-31)),
      };
      T result = 0;
      for(unsigned k = 1; k <= sizeof(d)/sizeof(d[0]); ++k)
      {
         result += (-d[k-1]*dz)/(k*dz + k*k);
      }
      return result;
   }

   template<class T>
   static T lanczos_sum_near_2(const T& dz)
   {
      lanczos_initializer<lanczos31UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      static const T d[30] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 147.9979641587472136175636384176549713358)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1638.404318611773924210055619836375434296)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 8480.981744216135641122944743711911653273)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -27274.93942104458448200467097634494071176)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 61076.98019918759324489193232276937262854)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -101158.8762737154296509560513952101409264)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 128491.1252383947174824913796141607174379)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -128087.2892038336581928787480535905496026)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 101717.5492545853663296795562084430123258)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -64957.8330410311808907869707511362206858)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 33536.59139229792478811870738772305570317)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -14026.01847115365926835980820243003785821)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4748.087094096186515212209389240715050212)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1296.477510211815971152205100242259733245)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 283.8099337545793198947620951499958085157)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -49.36969067101255103452092297769364837753)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 6.743492833270653628580811118017061581404)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.7121578704864048548351804794951487823626)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.0570092738016915476694118877057948681298)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.003373485297696102660302960722607722438643)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.0001429128843527532859999752593761934089751)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.41595867130858508233493767243236888636e-5)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.7883284669307241040059778207492255409785e-7)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.905786322462384670803148223703187214379e-9)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.5722790216999820323272452464661250331451e-11)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.1731510870832349779315841757234562309727e-13)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.2043890314358438601429048378015983874378e-16)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.6810185176079344204740000170500311171065e-20)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.3665567641131713928114853776588342403919e-24)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.1060655106553177007425710511436497259484e-29)),
      };
      T result = 0;
      T z = dz + 2;
      for(unsigned k = 1; k <= sizeof(d)/sizeof(d[0]); ++k)
      {
         result += (-d[k-1]*dz)/(z + k*z + k*k - 1);
      }
      return result;
   }

   static double g(){ return 32.08066999999999779902282170951366424561; }
};

//
// Lanczos Coefficients for N=61 G=63.192152
// Max experimental error (with 1000-bit precision arithmetic) 3.740e-113
// Generated with compiler: Microsoft Visual C++ version 8.0 on Win32 at Mar 12 2006
//
struct lanczos61UDT
{
   template <class T>
   static T lanczos_sum(const T& z)
   {
      lanczos_initializer<lanczos61UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      using namespace boost;
      static const T d[61] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2.50662827463100050241576528481104525300698674060993831662992357634229365460784197494659584)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 13349415823254323512107320481.3495396037261649201426994438803767191136434970492309775123879)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -300542621510568204264185787475.230003734889859348050696493467253861933279360152095861484548)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 3273919938390136737194044982676.40271056035622723775417608127544182097346526115858803376474)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -22989594065095806099337396006399.5874206181563663855129141706748733174902067950115092492439)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 116970582893952893160414263796102.542775878583510989850142808618916073286745084692189044738)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -459561969036479455224850813196807.283291532483532558959779434457349912822256480548436066098)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1450959909778264914956547227964788.89797179379520834974601372820249784303794436366366810477)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -3782846865486775046285288437885921.41537699732805465141128848354901016102326190612528503251)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 8305043213936355459145388670886540.09976337905520168067329932809302445437208115570138102767)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -15580988484396722546934484726970745.4927787160273626078250810989811865283255762028143642311)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 25262722284076250779006793435537600.0822901485517345545978818780090308947301031347345640449)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -35714428027687018805443603728757116.5304655170478705341887572982734901197345415291580897698)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 44334726194692443174715432419157343.2294160783772787096321009453791271387235388689346602833)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -48599573547617297831555162417695106.187829304963846482633791012658974681648157963911491985)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 47258466493366798944386359199482189.0753349196625125615316002614813737880755896979754845101)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -40913448215392412059728312039233342.142914753896559359297977982314043378636755884088383226)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 31626312914486892948769164616982902.7262756989418188077611392594232674722318027323102462687)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -21878079174441332123064991795834438.4699982361692990285700077902601657354101259411789722708)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 13567268503974326527361474986354265.3136632133935430378937191911532112778452274286122946396)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -7551494211746723529747611556474669.62996644923557605747803028485900789337467673523741066527)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 3775516572689476384052312341432597.70584966904950490541958869730702790312581801585742038997)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1696271471453637244930364711513292.79902955514107737992185368006225264329876265486853482449)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 684857608019352767999083000986166.20765273693720041519286231015176745354062413008561259139)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -248397566275708464679881624417990.410438107634139924805871051723444048539177890346227250473)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 80880368999557992138783568858556.1512378233079327986518410244522800950609595592170022878937)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -23618197945394013802495450485616.9025005749893350650829964098117490779655546610665927669729)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 6176884636893816103087134481332.06708966653493024119556843727320635285468825056891248447124)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1444348683723439589948246285262.64080678953468490544615312565485170860503207005915261691108)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 301342031656979076702313946827.961658905182634508217626783081241074250132289461989777865387)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -55959656587719766738301589651.3940625826610668990368881615587469329021742236397809951765678)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 9223339169004064297247180402.36227016155682738556103138196079389248843082157924368301293963)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1344882881571942601385730283.42710150182526891377514071881534880944872423492272147871101373)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 172841913316760599352601139.54409257740173055624405575900164401527761357324625574736896079)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -19496120443876233531343952.3802212016691702737346568192063937387825469602063310488794471653)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1920907372583710284097959.44121420322495784420169085871802458519363819782779653621724219067)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -164429314798240461613359.399597503536657962383155875723527581699785846599051112719962464604)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 12154026644351189572525.1452249886865981747374191977801688548318519692423556934568426042152)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -770443988366210815096.519382051977221101156336663806705367929328924137169970381042234329058)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 41558909851418707920.4696085656889424895313728719601503526476333404973280596225722152966128)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1890879946549708819.24562220042687554209318172044783707920086716716717574156283898330017796)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 71844996557297623.9583461685535340440524052492427928388171299145330229958643439878608673403)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -2253785109518255.55600197759875781765803818232939130127735487613049577235879610065545755637)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 57616883849355.997562563968344493719962252675875692642406455612671495250543228005045106721)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1182495730353.08218118278997948852215670614084013289033222774171548915344541249351599628436)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 19148649358.6196967288062261380599423925174178776792840639099120170800869284300966978300613)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -239779605.891370259668403359614360511661030470269478602533200704639655585967442891496784613)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2267583.00284368310957842936892685032434505866445291643236133553754152047677944820353796872)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -15749.490806784673108773558070497383604733010677027764233749920147549999361110299643477893)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 77.7059495149052727171505425584459982871343274332635726864135949842508025564999785370162956)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.261619987273930331397625130282851629108569607193781378836014468617185550622160348688297247)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.000572252321659691600529444769356185993188551770817110673186068921175991328434642504616377475)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.765167220661540041663007112207436426423746402583423562585653954743978584117929356523307954e-6)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.579179571056209077507916813937971472839851499147582627425979879366849876944438724610663401e-9)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.224804733043915149719206760378355636826808754704148660354494460792713189958510735070096991e-12)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.392711975389579343321746945135488409914483448652884894759297584020979857734289645857714768e-16)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.258603588346412049542768766878162221817684639789901440429511261589010049357907537684380983e-20)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.499992460848751668441190360024540741752242879565548017176883304716370989218399797418478685e-25)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.196211614533318174187346267877390498735734213905206562766348625767919122511096089367364025e-30)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.874722648949676363732094858062907290148733370978226751462386623191111439121706262759209573e-37)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.163907874717737848669759890242660846846105433791283903651924563157080252845636658802930428e-44)),
      };
      T result = d[0];
      for(unsigned k = 1; k < sizeof(d)/sizeof(d[0]); ++k)
      {
         result += d[k]/(z+(k-1));
      }
      return result;
   }

   template <class T>
   static T lanczos_sum_expG_scaled(const T& z)
   {
      lanczos_initializer<lanczos61UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      using namespace boost;
      static const T d[61] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.901751806425638853077358552989167785490911341809902155556127108480303870921448984935411583e-27)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4.80241125306810017699523302110401965428995345115391817406006361151407344955277298373661032)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -108.119283021710869401330097315436214587270846871451487282117128515476478251641970487922552)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1177.78262074811362219818923738088833932279000985161077740440010901595132448469513438139561)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -8270.43570321334374279057432173814835581983913167617217749736484999327758232081395983082867)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 42079.807161997077661752306902088979258826568702655699995911391774839958572703348502730394)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -165326.003834443330215001219988296482004968548294447320869281647211603153902436231468280089)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 521978.361504895300685499370463597042533432134369277742485307843747923127933979566742421213)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1360867.51629992863544553419296636395576666570468519805449755596254912681418267100657262281)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2987713.73338656161102517003716335104823650191612448011720936412226357385029800040631754755)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -5605212.64915921452169919008770165304171481697939254152852673009005154549681704553438450709)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 9088186.58332916818449459635132673652700922052988327069535513580836143146727832380184335474)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -12848155.5543636394746355365819800465364996596310467415907815393346205151090486190421959769)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 15949281.2867656960575878805158849857756293807220033618942867694361569866468996967761600898)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -17483546.9948295433308250581770557182576171673272450149400973735206019559576269174369907171)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 17001087.8599749419660906448951424280111036786456594738278573653160553115681287326064596964)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -14718487.0643665950346574802384331125115747311674609017568623694516187494204567579595827859)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 11377468.7255609717716845971105161298889777425898291183866813269222281486121330837791392732)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -7870571.64253038587947746661946939286858490057774448573157856145556080330153403858747655263)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4880783.08440908743641723492059912671377140680710345996273343885045364205895751515063844239)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -2716626.7992639625103140035635916455652302249897918893040695025407382316653674141983775542)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1358230.46602865696544327299659410214201327791319846880787515156343361311278133805428800255)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -610228.440751458395860905749312275043435828322076830117123636938979942213530882048883969802)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 246375.416501158654327780901087115642493055617468601787093268312234390446439555559050129729)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -89360.2599028475206119333931211015869139511677735549267100272095432140508089207221096740632)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 29096.4637987498328341260960356772198979319790332957125131055960448759586930781530063775634)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -8496.57401431514433694413130585404918350686834939156759654375188338156288564260152505382438)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2222.11523574301594407443285016240908726891841242444092960094015874546135316534057865883047)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -519.599993280949289705514822058693289933461756514489674453254304308040888101533569480646682)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 108.406868361306987817730701109400305482972790224573776407166683184990131682003417239181112)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -20.1313142142558596796857948064047373605439974799116521459977609253211918146595346493447238)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 3.31806787671783168020012913552384112429614503798293169239082032849759155847394955909684383)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.483817477111537877685595212919784447924875428848331771524426361483392903320495411973587861)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.0621793463102927384924303843912913542297042029136293808338022462765755471002366206711862637)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.00701366932085103924241526535768453911099671087892444015581511551813219720807206445462785293)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.000691040514756294308758606917671220770856269030526647010461217455799229645004351524024364997)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.591529398871361458428147660822525365922497109038495896497692806150033516658042357799869656e-4)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.437237367535177689875119370170434437030440227275583289093139147244747901678407875809020739e-5)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.277164853397051135996651958345647824709602266382721185838782221179129726200661453504250697e-6)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.149506899012035980148891401548317536032574502641368034781671941165064546410613201579653674e-7)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.68023824066463262779882895193964639544061678698791279217407325790147925675797085217462974e-9)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.258460163734186329938721529982859244969655253624066115559707985878606277800329299821882688e-10)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.810792256024669306744649981276512583535251727474303382740940985102669076169168931092026491e-12)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.207274966207031327521921078048021807442500113231320959236850963529304158700096495799022922e-13)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.425399199286327802950259994834798737777721414442095221716122926637623478450472871269742436e-15)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.688866766744198529064607574117697940084548375790020728788313274612845280173338912495478431e-17)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.862599751805643281578607291655858333628582704771553874199104377131082877406179933909898885e-19)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.815756005678735657200275584442908437977926312650210429668619446123450972547018343768177988e-21)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.566583084099007858124915716926967268295318152203932871370429534546567151650626184750291695e-23)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.279544761599725082805446124351997692260093135930731230328454667675190245860598623539891708e-25)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.941169851584987983984201821679114408126982142904386301937192011680047611188837432096199601e-28)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.205866011331040736302780507155525142187875191518455173304638008169488993406425201915370746e-30)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.27526655245712584371295491216289353976964567057707464008951584303679019796521332324352501e-33)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.208358067979444305082929004102609366169534624348056112144990933897581971394396210379638792e-36)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.808728107661779323263133007119729988596844663194254976820030366188579170595441991680169012e-40)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.141276924383478964519776436955079978012672985961918248012931336621229652792338950573694356e-43)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.930318449287651389310440021745842417218125582685428432576258687100661462527604238849332053e-48)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.179870748819321661641184169834635133045197146966203370650788171790610563029431722308057539e-52)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.705865243912790337263229413370018392321238639017433365017168104310561824133229343750737083e-58)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.3146787805734405996448268100558028857930560442377698646099945108125281507396722995748398e-64)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.589653534231618730406843260601322236697428143603814281282790370329151249078338470962782338e-72)),
      };
      T result = d[0];
      for(unsigned k = 1; k < sizeof(d)/sizeof(d[0]); ++k)
      {
         result += d[k]/(z+(k-1));
      }
      return result;
   }

   template<class T>
   static T lanczos_sum_near_1(const T& dz)
   {
      lanczos_initializer<lanczos61UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      using namespace boost;
      static const T d[60] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 23.2463658527729692390378860713647146932236940604550445351214987229819352880524561852919518)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -523.358012551815715084547614110229469295755088686612838322817729744722233637819564673967396)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 5701.12892340421080714956066268650092612647620400476183901625272640935853188559347587495571)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -40033.5506451901904954336453419007623117537868026994808919201793803506999271787018654246699)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 203689.884259074923009439144410340256983393397995558814367995938668111650624499963153485034)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -800270.648969745331278757692597096167418585957703057412758177038340791380011708874081291202)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 2526668.23380061659863999395867315313385499515711742092815402701950519696944982260718031476)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -6587362.57559198722630391278043503867973853468105110382293763174847657538179665571836023631)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 14462211.3454541602975917764900442754186801975533106565506542322063393991552960595701762805)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -27132375.1879227404375395522940895789625516798992585980380939378508607160857820002128106898)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 43991923.8735251977856804364757478459275087361742168436524951824945035673768875988985478116)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -62192284.0030124679010201921841372967696262036115679150017649233887633598058364494608060812)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 77203473.0770033513405070667417251568915937590689150831268228886281254637715669678358204991)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -84630180.2217173903516348977915150565994784278120192219937728967986198118628659866582594874)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 82294807.2253549409847505891112074804416229757832871133388349982640444405470371147991706317)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -71245738.2484649177928765605893043553453557808557887270209768163561363857395639001251515788)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 55073334.3180266913441333534260714059077572215147571872597651029894142803987981342430068594)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -38097984.1648990787690036742690550656061009857688125101191167768314179751258568264424911474)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 23625729.5032184580395130592017474282828236643586203914515183078852982915252442161768527976)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -13149998.4348054726172055622442157883429575511528431835657668083088902165366619827169829685)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 6574597.77221556423683199818131482663205682902023554728024972161230111356285973623550338976)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -2953848.1483469149918109110050192571921018042012905892107136410603990336401921230407043408)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1192595.29584357246380113611351829515963605337523874715861849584306265512819543347806085356)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -432553.812019608638388918135375154289816441900572658692369491476137741687213006403648722272)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 140843.215385933866391177743292449477205328233960902455943995092958295858485718885800427129)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -41128.186992679630058614841985110676526199977321524879849001760603476646382839182691529968)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 10756.2849191854701631989789887757784944313743544315113894758328432005767448056040879337769)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -2515.15559672041299884426826962296210458052543246529646213159169885444118227871246315458787)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 524.750087004805200600237632074908875763734578390662349666321453103782638818305404274166951)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -97.4468596421732493988298219295878130651986131393383646674144877163795143930682205035917965)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 16.0613108128210806736384551896802799172445298357754547684100294231532127326987175444453058)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -2.34194813526540240672426202485306862230641838409943369059203455578340880900483887447559874)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.300982934748016059399829007219431333744032924923669397318820178988611410275964499475465815)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.033950095985367909789000959795708551814461844488183964432565731809399824963680858993718525)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.00334502394288921146242772614150438404658527112198421937945605441697314216921393987758378122)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.000286333429067523984607730553301991502191011265745476190940771685897687956262049750683013485)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.211647426149364947402896718485536530479491688838087899435991994237067890628274492042231115e-4)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.134163345121302758109675190598169832775248626443483098532368562186356128620805552609175683e-5)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.723697303042715085329782938306424498336642078597508935450663080894255773653328980495047891e-7)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.329273487343139063533251321553223583999676337945788660475231347828772272134656322947906888e-8)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.12510922551028971731767784013117088894558604838820475961392154031378891971216135267744134e-9)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.392468958215589939603666430583400537413757786057185505426804034745840192914621891690369903e-11)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.100332717101049934370760667782927946803279422001380028508200697081188326364078428184546051e-12)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.205917088291197705194762747639836655808855850989058813560983717575008725393428497910009756e-14)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.333450178247893143608439314203175490705783992567107481617660357577257627854979230819461489e-16)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.417546693906616047110563550428133589051498362676394888715581845170969319500638944065594319e-18)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.394871691642184410859178529844325390739857256666676534513661579365702353214518478078730801e-20)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.274258012587811199757875927548699264063511843669070634471054184977334027224611843434000922e-22)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.135315354265459854889496635967343027244391821142592599244505313738163473730636430399785048e-24)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.455579032003288910408487905303223613382276173706542364543918076752861628464036586507967767e-27)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.99650703862462739161520123768147312466695159780582506041370833824093136783202694548427718e-30)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.1332444609228706921659395801935919548447859029572115502899861345555006827214220195650058e-32)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.100856999148765307000182397631280249632761913433008379786888200467467364474581430670889392e-35)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.39146979455613683472384690509165395074425354524713697428673406058157887065953366609738731e-39)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.683859606707931248105140296850112494069265272540298100341919970496564103098283709868586478e-43)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.450326344248604222735147147805963966503893913752040066400766411031387063854141246972061792e-47)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.870675378039492904184581895322153006461319724931909799151743284769901586333730037761678891e-52)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.341678395249272265744518787745356400350877656459401143889000625280131819505857966769964401e-57)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.152322191370871666358069530949353871960316638394428595988162174042653299702098929238880862e-63)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.285425405297633795767452984791738825078111150078605004958179057245980222485147999495352632e-71)),
      };
      T result = 0;
      for(unsigned k = 1; k <= sizeof(d)/sizeof(d[0]); ++k)
      {
         result += (-d[k-1]*dz)/(k*dz + k*k);
      }
      return result;
   }

   template<class T>
   static T lanczos_sum_near_2(const T& dz)
   {
      lanczos_initializer<lanczos61UDT, T>::force_instantiate(); // Ensure our constants get initialized before main()
      using namespace boost;
      static const T d[60] = {
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 557.56438192770795764344217888434355281097193198928944200046501607026919782564033547346298)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -12552.748616427645475141433405567201788681683808077269330800392600825597799119572762385222)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 136741.650054039199076788077149441364242294724343897779563222338447737802381279007988884806)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -960205.223613240309942047656967301131022760634321049075674684679438471862998829007639437133)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 4885504.47588736223774859617054275229642041717942140469884121916073195308537421162982679289)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -19194501.738192166918904824982935279260356575935661514109550613809352009246483412530545583)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 60602169.8633537742937457094837494059849674261357199218329545854990149896822944801504450843)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -157997975.522506767297528502540724511908584668874487506510120462561270100749019845014382885)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 346876323.86092543685419723290495817330608574729543216092477261152247521712190505658568876)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -650770365.471136883718747607976242475416651908858429752332176373467422603953536408709972919)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1055146856.05909309330903130910708372830487315684258450293308627289334336871273080305128138)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1491682726.25614447429071368736790697283307005456720192465860871846879804207692411263924608)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1851726287.94866167094858600116562210167031458934987154557042242638980748286188183300900268)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -2029855953.68334371445800569238095379629407314338521720558391277508374519526853827142679839)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1973842002.53354868177824629525448788555435194808657489238517523691040148611221295436287925)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -1708829941.98209573247426625323314413060108441455314880934710595647408841619484540679859098)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 1320934627.12433688699625456833930317624783222321555050330381730035733198249283009357314954)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -913780636.858542526294419197161614811332299249415125108737474024007693329922089123296358727)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 566663423.929632170286007468016419798879660054391183200464733820209439185545886930103546787)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -315402880.436816230388857961460509181823167373029384218959199936902955049832392362044305869)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 157691811.550465734461741500275930418786875005567018699867961482249002625886064187146134966)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -70848085.5705405970640618473551954585013808128304384354476488268600720054598122945113512731)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 28604413.4050137708444142264980840059788755325900041515286382001704951527733220637586013815)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -10374808.7067303054787164054055989420809074792801592763124972441820101840292558840131568633)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 3378126.32016207486657791623723515804931231041318964254116390764473281291389374196880720069)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -986460.090390653140964189383080344920103075349535669020623874668558777188889544398718979744)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 257989.631187387317948158483575125380011593209850756066176921901006833159795100137743395985)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -60326.0391159227288325790327830741260824763549807922845004854215660451399733578621565837087)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 12586.1375399649496159880821645216260841794563919652590583420570326276086323953958907053394)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -2337.26417330316922535871922886167444795452055677161063205953141105726549966801856628447293)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 385.230745012608736644117458716226876976056390433401632749144285378123105481506733917763829)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -56.1716559403731491675970177460841997333796694857076749852739159067307309470690838101179615)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 7.21907953468550196348585224042498727840087634483369357697580053424523903859773769748599575)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.814293485887386870805786409956942772883474224091975496298369877683530509729332902182019049)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.0802304419995150047616460464220884371214157889148846405799324851793571580868840034085001373)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.00686771095380619535195996193943858680694970000948742557733102777115482017857981277171196115)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.000507636621977556438232617777542864427109623356049335590894564220687567763620803789858345916)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.32179095465362720747836116655088181481893063531138957363431280817392443948706754917605911e-4)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.173578890579848508947329833426585354230744194615295570820295052665075101971588563893718407e-5)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.789762880006288893888161070734302768702358633525134582027140158619195373770299678322596835e-7)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.300074637200885066788470310738617992259402710843493097610337134266720909870967550606601658e-8)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.941337297619721713151110244242536308266701344868601679868536153775533330272973088246835359e-10)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.24064815013182536657310186836079333949814111498828401548170442715552017773994482539471456e-11)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.493892399304811910466345686492277504628763169549384435563232052965821874553923373100791477e-13)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.799780678476644196901221989475355609743387528732994566453160178199295104357319723742820593e-15)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.100148627870893347527249092785257443532967736956154251497569191947184705954310833302770086e-16)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.947100256812658897084619699699028861352615460106539259289295071616221848196411749449858071e-19)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.657808193528898116367845405906343884364280888644748907819280236995018351085371701094007759e-21)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.324554050057463845012469010247790763753999056976705084126950591088538742509983426730851614e-23)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.10927068902162908990029309141242256163212535730975970310918370355165185052827948996110107e-25)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.239012340507870646690121104637467232366271566488184795459093215303237974655782634371712486e-28)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.31958700972990573259359660326375143524864710953063781494908314884519046349402409667329667e-31)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.241905641292988284384362036555782113275737930713192053073501265726048089991747342247551645e-34)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.93894080230619233745797029179332447129464915420290457418429337322820997038069119047864035e-38)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.164023814025085488413251990798690797467351995518990067783355251949198292596815470576539877e-41)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.108010831192689925518484618970761942019888832176355541674171850211917230280206410356465451e-45)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.208831600642796805563854019033577205240227465154130766898180386564934443551840379116390645e-50)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.819516067465171848863933747691434138146789031214932473898084756489529673230665363014007306e-56)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, 0.365344970579318347488211604761724311582675708113250505307342682118101409913523622073678179e-62)),
         static_cast<T>(BOOST_MATH_HUGE_CONSTANT(T, 150, -0.684593199208628857931267904308244537968349564351534581234005234847904343404822808648361291e-70)),
      };
      T result = 0;
      T z = dz + 2;
      for(unsigned k = 1; k <= sizeof(d)/sizeof(d[0]); ++k)
      {
         result += (-d[k-1]*dz)/(z + k*z + k*k - 1);
      }
      return result;
   }

   static double g(){ return 63.19215200000000010049916454590857028961181640625; }
};

}}} // namespaces

#endif


