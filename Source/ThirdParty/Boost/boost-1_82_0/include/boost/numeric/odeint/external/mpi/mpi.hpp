/*
 [auto_generated]
 boost/numeric/odeint/external/mpi/mpi.hpp

 [begin_description]
 Wrappers for MPI.
 [end_description]

 Copyright 2013 <PERSON><PERSON>
 Copyright 2013 <PERSON>
 Copyright 2013 Pascal Germroth

 Distributed under the Boost Software License, Version 1.0.
 (See accompanying file LICENSE_1_0.txt or
 copy at http://www.boost.org/LICENSE_1_0.txt)
 */


#ifndef BOOST_NUMERIC_ODEINT_EXTERNAL_MPI_MPI_HPP_INCLUDED
#define BOOST_NUMERIC_ODEINT_EXTERNAL_MPI_MPI_HPP_INCLUDED

#include <boost/numeric/odeint/external/mpi/mpi_vector_state.hpp>
#include <boost/numeric/odeint/external/mpi/mpi_nested_algebra.hpp>

#endif
