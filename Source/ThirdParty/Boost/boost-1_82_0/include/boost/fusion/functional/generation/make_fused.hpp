/*=============================================================================
    Copyright (c) 2007 Tobias <PERSON>winger
  
    Use modification and distribution are subject to the Boost Software 
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt).
==============================================================================*/

#if !defined(BOOST_FUSION_FUNCTIONAL_GENERATION_MAKE_FUSED_HPP_INCLUDED)
#define BOOST_FUSION_FUNCTIONAL_GENERATION_MAKE_FUSED_HPP_INCLUDED

#include <boost/fusion/support/config.hpp>
#include <boost/fusion/functional/adapter/fused.hpp>

#define BOOST_FUSION_CLASS_TPL_NAME fused
#include <boost/fusion/functional/generation/detail/gen_make_adapter.hpp>

#endif

