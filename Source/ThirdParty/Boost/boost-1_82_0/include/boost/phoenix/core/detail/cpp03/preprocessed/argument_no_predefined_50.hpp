/*==============================================================================
    Copyright (c) 2001-2010 <PERSON>
    Copyright (c) 2010-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    namespace placeholders
    {
        typedef expression::argument<1>::type arg1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type arg2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type arg3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type arg4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type arg5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type arg6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type arg7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type arg8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type arg9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type arg10_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<11>::type arg11_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<12>::type arg12_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<13>::type arg13_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<14>::type arg14_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<15>::type arg15_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<16>::type arg16_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<17>::type arg17_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<18>::type arg18_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<19>::type arg19_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<20>::type arg20_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<21>::type arg21_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<22>::type arg22_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<23>::type arg23_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<24>::type arg24_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<25>::type arg25_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<26>::type arg26_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<27>::type arg27_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<28>::type arg28_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<29>::type arg29_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<30>::type arg30_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<31>::type arg31_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<32>::type arg32_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<33>::type arg33_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<34>::type arg34_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<35>::type arg35_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<36>::type arg36_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<37>::type arg37_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<38>::type arg38_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<39>::type arg39_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<40>::type arg40_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<41>::type arg41_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<42>::type arg42_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<43>::type arg43_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<44>::type arg44_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<45>::type arg45_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<46>::type arg46_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<47>::type arg47_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<48>::type arg48_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<49>::type arg49_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<50>::type arg50_type BOOST_ATTRIBUTE_UNUSED;
        typedef expression::argument<1>::type _1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type _2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type _3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type _4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type _5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type _6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type _7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type _8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type _9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type _10_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<11>::type _11_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<12>::type _12_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<13>::type _13_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<14>::type _14_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<15>::type _15_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<16>::type _16_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<17>::type _17_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<18>::type _18_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<19>::type _19_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<20>::type _20_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<21>::type _21_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<22>::type _22_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<23>::type _23_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<24>::type _24_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<25>::type _25_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<26>::type _26_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<27>::type _27_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<28>::type _28_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<29>::type _29_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<30>::type _30_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<31>::type _31_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<32>::type _32_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<33>::type _33_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<34>::type _34_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<35>::type _35_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<36>::type _36_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<37>::type _37_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<38>::type _38_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<39>::type _39_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<40>::type _40_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<41>::type _41_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<42>::type _42_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<43>::type _43_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<44>::type _44_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<45>::type _45_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<46>::type _46_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<47>::type _47_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<48>::type _48_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<49>::type _49_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<50>::type _50_type BOOST_ATTRIBUTE_UNUSED;
    }
    namespace arg_names
    {
        typedef expression::argument<1>::type arg1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type arg2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type arg3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type arg4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type arg5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type arg6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type arg7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type arg8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type arg9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type arg10_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<11>::type arg11_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<12>::type arg12_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<13>::type arg13_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<14>::type arg14_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<15>::type arg15_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<16>::type arg16_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<17>::type arg17_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<18>::type arg18_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<19>::type arg19_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<20>::type arg20_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<21>::type arg21_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<22>::type arg22_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<23>::type arg23_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<24>::type arg24_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<25>::type arg25_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<26>::type arg26_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<27>::type arg27_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<28>::type arg28_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<29>::type arg29_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<30>::type arg30_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<31>::type arg31_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<32>::type arg32_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<33>::type arg33_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<34>::type arg34_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<35>::type arg35_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<36>::type arg36_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<37>::type arg37_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<38>::type arg38_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<39>::type arg39_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<40>::type arg40_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<41>::type arg41_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<42>::type arg42_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<43>::type arg43_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<44>::type arg44_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<45>::type arg45_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<46>::type arg46_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<47>::type arg47_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<48>::type arg48_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<49>::type arg49_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<50>::type arg50_type BOOST_ATTRIBUTE_UNUSED;
        typedef expression::argument<1>::type _1_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<2>::type _2_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<3>::type _3_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<4>::type _4_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<5>::type _5_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<6>::type _6_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<7>::type _7_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<8>::type _8_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<9>::type _9_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<10>::type _10_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<11>::type _11_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<12>::type _12_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<13>::type _13_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<14>::type _14_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<15>::type _15_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<16>::type _16_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<17>::type _17_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<18>::type _18_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<19>::type _19_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<20>::type _20_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<21>::type _21_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<22>::type _22_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<23>::type _23_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<24>::type _24_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<25>::type _25_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<26>::type _26_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<27>::type _27_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<28>::type _28_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<29>::type _29_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<30>::type _30_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<31>::type _31_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<32>::type _32_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<33>::type _33_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<34>::type _34_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<35>::type _35_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<36>::type _36_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<37>::type _37_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<38>::type _38_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<39>::type _39_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<40>::type _40_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<41>::type _41_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<42>::type _42_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<43>::type _43_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<44>::type _44_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<45>::type _45_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<46>::type _46_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<47>::type _47_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<48>::type _48_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<49>::type _49_type BOOST_ATTRIBUTE_UNUSED; typedef expression::argument<50>::type _50_type BOOST_ATTRIBUTE_UNUSED;
    }
