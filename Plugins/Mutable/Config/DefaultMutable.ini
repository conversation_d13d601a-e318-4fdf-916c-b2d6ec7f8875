; Settings for the Mutable plugin
[Features]

; If CustomizableObjectNumBoneInfluences is set to N, CustomizableObjects will be allowed to use a max of N bone influences 
; per vertex. The only valid values are 4, 8, 12, Four, Eight, Twelve. 
; If r.GPUSkin.UnlimitedBoneInfluencesThreshold is set to a lower number, then only that number of influences
; will always be used and allocated thus saving memory per vertex when the real number is higher for a vertex.
; If CustomizableObjectNumBoneInfluences is not defined, only the default 4 will be used. This is used at object compile time.
;CustomizableObjectNumBoneInfluences=Eight

; For backwards compatibility bExtraBoneInfluencesEnabled can be used instead, but will be overridden by 
; CustomizableObjectNumBoneInfluences if both are present. If enabled CustomizableObjects will be allowed to 
; use 8 bone influences per vertex instead of the default 4. This is used at object compile time.
bExtraBoneInfluencesEnabled=False

[EditorDefaults]
ClipMorphMaterialName=/Engine/EditorMaterials/LevelGridMaterial.LevelGridMaterial
ClipMeshMaterialName=/Mutable/Materials/ClipMeshMaterial.ClipMeshMaterial
DynamicallyGenerated_DGGUI_Path=/Game/UI/DynamicallyGeneratedGUI_DGGUI/DynamicallyGeneratedGUI_DGGUI.DynamicallyGeneratedGUI_DGGUI_C

;--------------------------------------------------------------------------------------------------
; These are scalability settings that can be defined in any xxxScalability.ini file.
[ScalabilitySettings]

; If different than 0, limit the amount of memory (in KB) to use to cache streaming data when 
; building characters. 0 means no limit, -1 means use default (10Mb in PC and 3Mb in consoles).
+CVars=b.MutableStreamingMemory=10000

; Override in platform scalability .ini files
; b.MutableStreamingMemory=3000

[ViewDistanceQuality@0]
; If different than 0, limit the number of mutable instances with full LODs to have at a given time.
+CVars=b.NumGeneratedInstancesLimit=7

; If different than 0, limit the number of mutable instances with LOD 1 to have at a given time.
+CVars=b.NumGeneratedInstancesLimitLOD1=0

; If different than 0, limit the number of mutable instances with LOD 2 to have at a given time.
+CVars=b.NumGeneratedInstancesLimitLOD2=0

; If NumGeneratedInstancesLimit is different than 0, sets the distance at which the system will fix 
; the LOD of an instance to the lowest res one (LOD2) to prevent unnecessary LOD changes and memory 
; consumption
+CVars=b.DistanceForFixedLOD2=0

[ViewDistanceQuality@1]
+CVars=b.NumGeneratedInstancesLimit=0
[ViewDistanceQuality@2]
+CVars=b.NumGeneratedInstancesLimit=0
[ViewDistanceQuality@3]
+CVars=b.NumGeneratedInstancesLimit=0
[ViewDistanceQuality@Cine]
+CVars=b.NumGeneratedInstancesLimit=0
