<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>ONNXRuntime</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: ONNXRuntime
    Version: 1.17.1-->
<Location>/Engine/Plugins/NNE/NNERuntimeORT/Source/ThirdParty/Onnxruntime/</Location>
  <Function>The software used to run neural network inference through the onnx runtime backend and also to optimize ML models.</Function>
  <Eula>https://github.com/microsoft/onnxruntime/blob/v1.17.1/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 


 