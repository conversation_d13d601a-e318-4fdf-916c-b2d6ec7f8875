<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Triton Inference Server Client</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: Triton Inference Server & Client
    Download Link: https://github.com/triton-inference-server
    Version: 
    Note: 
    -->
<Location>/Engine/Plugins/NNE/NNERuntimeORT/Source/ThirdParty/Onnxruntime/</Location>
<Function>The software is part of the Onnxruntime library, which is used to run neural network inference through the onnx runtime backend and also to optimize ML models.</Function>
<Eula>https://github.com/triton-inference-server/server/blob/main/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 


 