// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;
using System;

public class Harmonix : ModuleRules
{
	public Harmonix(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"AudioExtensions",
				"DeveloperSettings"
			});


		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"CoreUObject",
				"Engine",
				"Slate",
				"SlateCore",
			});
	}
}
