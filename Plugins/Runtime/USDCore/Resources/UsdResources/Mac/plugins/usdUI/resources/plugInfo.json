# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdUIBackdrop": {
                        "alias": {
                            "UsdSchemaBase": "Backdrop"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdUINodeGraphNodeAPI": {
                        "alias": {
                            "UsdSchemaBase": "NodeGraphNodeAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdUISceneGraphPrimAPI": {
                        "alias": {
                            "UsdSchemaBase": "SceneGraphPrimAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdUI.dylib", 
            "Name": "usdUI", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
