{"Plugins": [{"Info": {"SdfMetadata": {"faceIndexPrimvar": {"appliesTo": ["attributes"], "default": "ptexFaceIndex", "documentation": "Specifies an array of face indices used for ptex mapping", "type": "token"}, "faceOffsetPrimvar": {"appliesTo": ["attributes"], "default": "ptexFaceOffset", "documentation": "Specifies the ptex face index offset for aggregated ptex files", "type": "token"}, "uvPrimvar": {"appliesTo": ["attributes"], "default": "", "documentation": "Specifies the UV primvar for texture mapping", "type": "token"}}, "Types": {"UsdImagingBasisCurvesAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "BasisCurves"}, "UsdImagingCameraAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "Camera"}, "UsdImagingCapsuleAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "includeSchemaFamily": true, "primTypeName": "Capsule"}, "UsdImagingCollectionAPIAdapter": {"bases": ["UsdImagingAPISchemaAdapter"], "isInternal": true, "apiSchemaName": "CollectionAPI"}, "UsdImagingConeAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Cone"}, "UsdImagingCoordSysAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "coordSys"}, "UsdImagingCoordSysAPIAdapter": {"bases": ["UsdImagingAPISchemaAdapter"], "isInternal": true, "apiSchemaName": "CoordSysAPI"}, "UsdImagingCubeAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "C<PERSON>"}, "UsdImagingCylinderAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "includeSchemaFamily": true, "primTypeName": "<PERSON><PERSON><PERSON>"}, "UsdImagingDrawModeAdapter": {"bases": ["UsdImagingInstanceablePrimAdapter"], "isInternal": true, "primTypeName": "__drawModeAdapter"}, "UsdImagingGeomSubsetAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "GeomSubset"}, "UsdImagingHermiteCurvesAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "HermiteCurves"}, "UsdImagingMaterialAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "Material"}, "UsdImagingShaderAdapter": {"bases": ["UsdImagingRepresentedByAncestorPrimAdapter"], "isInternal": true, "primTypeName": "Shader"}, "UsdImagingNodeGraphAdapter": {"bases": ["UsdImagingRepresentedByAncestorPrimAdapter"], "isInternal": true, "primTypeName": "NodeGraph"}, "UsdImagingMaterialBindingAPIAdapter": {"bases": ["UsdImagingAPISchemaAdapter"], "isInternal": true, "apiSchemaName": "MaterialBindingAPI"}, "UsdImagingMeshAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "<PERSON><PERSON>"}, "UsdImagingTetMeshAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "<PERSON><PERSON><PERSON><PERSON>"}, "UsdImagingNurbsCurvesAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "NurbsCurves"}, "UsdImagingNurbsPatchAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "NurbsPatch"}, "UsdImagingPlaneAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Plane"}, "UsdImagingPointsAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Points"}, "UsdImagingPointInstancerAdapter": {"bases": ["UsdImagingInstanceablePrimAdapter"], "isInternal": true, "primTypeName": "PointInstancer"}, "UsdImagingSphereAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Sphere"}, "UsdImagingRenderSettingsAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "RenderSettings"}, "UsdImagingRenderProductAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "RenderProduct"}, "UsdImagingRenderVarAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "RenderVar"}, "UsdImagingVolumeAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Volume"}, "UsdImagingLightAdapter": {"bases": ["UsdImagingInstanceablePrimAdapter"], "isInternal": true, "primTypeName": "LightAPI", "includeDerivedPrimTypes": true}, "UsdImagingLightAPIAdapter": {"bases": ["UsdImagingAPISchemaAdapter"], "isInternal": true, "apiSchemaName": "LightAPI"}, "UsdImagingLightFilterAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "LightFilter", "includeDerivedPrimTypes": true}, "UsdImagingDomeLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "DomeLight"}, "UsdImagingDomeLight_1Adapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "DomeLight_1"}, "UsdImagingRectLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "RectLight"}, "UsdImagingSphereLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "SphereLight"}, "UsdImagingCylinderLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "CylinderLight"}, "UsdImagingDiskLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "DiskLight"}, "UsdImagingDistantLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "DistantLight"}, "UsdImagingPluginLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "PluginLight"}, "UsdImagingGeometryLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "GeometryLight"}, "UsdImagingPortalLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "PortalLight"}, "UsdImagingPluginLightFilterAdapter": {"bases": ["UsdImagingLightFilterAdapter"], "isInternal": true, "primTypeName": "PluginLightFilter"}, "UsdImagingGeomModelAPIAdapter": {"bases": ["UsdImagingAPISchemaAdapter"], "isInternal": true, "apiSchemaName": "GeomModelAPI"}}}, "LibraryPath": "../../../../../Source/ThirdParty/Linux/bin/x86_64-unknown-linux-gnu/libusd_usdImaging.so", "Name": "usdImaging", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}