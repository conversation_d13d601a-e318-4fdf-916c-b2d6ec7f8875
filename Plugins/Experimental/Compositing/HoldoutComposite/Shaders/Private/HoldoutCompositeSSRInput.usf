// Copyright Epic Games, Inc. All Rights Reserved.

#include "/Engine/Private/Common.ush"
#include "/Engine/Private/ScreenPass.ush"
#include "/Engine/Private/LensDistortion.ush"

SCREEN_PASS_TEXTURE_VIEWPORT(Input)
SCREEN_PASS_TEXTURE_VIEWPORT(Custom)
SCREEN_PASS_TEXTURE_VIEWPORT(Output)

Texture2D InputTexture;
SamplerState InputSampler;

Texture2D CustomTexture;
SamplerState CustomSampler;

Texture2D<float2> DistortingDisplacementTexture;
SamplerState DistortingDisplacementSampler;

Texture2D<float2> UndistortingDisplacementTexture;
SamplerState UndistortingDisplacementSampler;

float LastGlobalExposure;

void MainPS(
    float4 SvPosition : SV_POSITION,
    out float4 OutColor : SV_Target0
    )
{
    float2 ViewportUV = (SvPosition.xy - Output_ViewportMin) * Output_ViewportSizeInverse;
    const float2 DistortedViewportUV = ApplyLensDistortionOnViewportUV(DistortingDisplacementTexture, DistortingDisplacementSampler, ViewportUV);
    
    const float2 InputUV = (DistortedViewportUV * Input_ViewportSize + Input_ViewportMin) * Input_ExtentInverse;
    float4 SceneColor = Texture2DSample(InputTexture, InputSampler, InputUV);

    const float2 CustomUV = (ViewportUV * Custom_ViewportSize + Custom_ViewportMin) * Custom_ExtentInverse;
    float4 CustomColor = Texture2DSample(CustomTexture, CustomSampler, CustomUV);

    CustomColor.rgb *= LastGlobalExposure;
    OutColor.rgb = CustomColor.rgb * SceneColor.a + SceneColor.rgb;
    OutColor.a = CustomColor.a * SceneColor.a;
}
